@echo off
echo ========================================
echo Milvus向量数据库启动脚本
echo ========================================

echo.
echo 1. 检查Docker是否运行...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动，请先安装并启动Docker
    pause
    exit /b 1
)
echo Docker已就绪

echo.
echo 2. 启动Milvus服务...
docker-compose up -d

echo.
echo 3. 等待服务启动...
timeout /t 30 /nobreak >nul

echo.
echo 4. 检查服务状态...
docker-compose ps

echo.
echo ========================================
echo Milvus服务启动完成！
echo ========================================
echo.
echo 服务地址:
echo - Milvus gRPC: localhost:19530
echo - Milvus HTTP: localhost:9091
echo - Attu管理界面: http://localhost:3000
echo - MinIO控制台: http://localhost:9001
echo.
echo 默认账号密码:
echo - MinIO: minioadmin / minioadmin
echo.
echo 测试API:
echo - 连接测试: GET http://localhost:8080/knowledge/milvus/test/connection
echo - 存储测试: POST http://localhost:8080/knowledge/milvus/test/store
echo - 搜索测试: POST http://localhost:8080/knowledge/milvus/test/search
echo.
echo 按任意键继续...
pause >nul
