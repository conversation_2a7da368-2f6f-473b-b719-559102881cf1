# Milvus配置文件
etcd:
  endpoints:
    - etcd:2379

minio:
  address: minio
  port: 9000
  accessKeyID: minioadmin
  secretAccessKey: minioadmin
  useSSL: false
  bucketName: "a-bucket"
  rootPath: "files"

# 日志配置
log:
  level: info
  file:
    rootPath: ""
    maxSize: 300
    maxAge: 10
    maxBackups: 20

# 服务器配置
grpc:
  serverMaxRecvSize: 268435456
  serverMaxSendSize: 268435456
  clientMaxRecvSize: 268435456
  clientMaxSendSize: 268435456

# 数据节点配置
dataNode:
  dataSync:
    flowGraph:
      maxQueueLength: 1024
      maxParallelism: 1024

# 查询节点配置
queryNode:
  stats:
    publishInterval: 1000
  segcore:
    chunkRows: 1024

# 索引节点配置
indexNode:
  scheduler:
    buildParallel: 1

# 代理配置
proxy:
  timeTickInterval: 200
  msgStream:
    timeTick:
      bufSize: 512

# 根协调器配置
rootCoord:
  minSegmentSizeToEnableIndex: 1024

# 数据协调器配置
dataCoord:
  segment:
    maxSize: 512
    sealProportion: 0.12
    assignmentExpiration: 2000
    maxLife: 86400
  compaction:
    enableAutoCompaction: true

# 查询协调器配置
queryCoord:
  autoHandoff: true
  autoBalance: true
  overloadedMemoryThresholdPercentage: 90
  balanceIntervalSeconds: 60
  memoryUsageMaxDifferencePercentage: 30
