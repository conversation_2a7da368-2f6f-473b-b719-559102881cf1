package com.ruoyi.knowledge.store;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.collection.LoadCollectionParam;
import io.milvus.param.dml.InsertParam;
import io.milvus.param.dml.SearchParam;
import io.milvus.grpc.SearchResults;
import io.milvus.response.SearchResultsWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Milvus向量存储实现
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
public class MilvusEmbeddingStore implements EmbeddingStore<TextSegment> {

    private static final Logger logger = LoggerFactory.getLogger(MilvusEmbeddingStore.class);

    private final MilvusServiceClient milvusClient;
    private final String collectionName;
    private final int vectorDimension;

    public MilvusEmbeddingStore(MilvusServiceClient milvusClient, String collectionName, int vectorDimension) {
        this.milvusClient = milvusClient;
        this.collectionName = collectionName;
        this.vectorDimension = vectorDimension;
        
        // 确保集合已加载
        loadCollection();
    }

    /**
     * 加载集合到内存
     */
    private void loadCollection() {
        try {
            LoadCollectionParam loadParam = LoadCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();
            milvusClient.loadCollection(loadParam);
            logger.info("集合 {} 已加载到内存", collectionName);
        } catch (Exception e) {
            logger.error("加载集合失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public String add(Embedding embedding) {
        return add(embedding, null);
    }

    @Override
    public void add(String id, Embedding embedding) {
        add(embedding, null);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        try {
            List<List<Float>> vectors = Collections.singletonList(embedding.vectorAsList());
            List<String> texts = Collections.singletonList(textSegment != null ? textSegment.text() : "");
            
            // 从元数据中提取信息
            Long knowledgeBaseId = 0L;
            Long documentId = 0L;
            String documentName = "";
            
            if (textSegment != null && textSegment.metadata() != null) {
                String kbIdStr = textSegment.metadata().getString("knowledgeBaseId");
                String docIdStr = textSegment.metadata().getString("documentId");
                documentName = textSegment.metadata().getString("documentName");
                
                if (kbIdStr != null) {
                    try {
                        knowledgeBaseId = Long.parseLong(kbIdStr);
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析knowledgeBaseId: {}", kbIdStr);
                    }
                }
                
                if (docIdStr != null) {
                    try {
                        documentId = Long.parseLong(docIdStr);
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析documentId: {}", docIdStr);
                    }
                }
                
                if (documentName == null) {
                    documentName = "";
                }
            }

            List<Long> knowledgeBaseIds = Collections.singletonList(knowledgeBaseId);
            List<Long> documentIds = Collections.singletonList(documentId);
            List<String> documentNames = Collections.singletonList(documentName);

            InsertParam insertParam = InsertParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFields(Arrays.asList(
                            new InsertParam.Field("vector", vectors),
                            new InsertParam.Field("text", texts),
                            new InsertParam.Field("knowledge_base_id", knowledgeBaseIds),
                            new InsertParam.Field("document_id", documentIds),
                            new InsertParam.Field("document_name", documentNames)
                    ))
                    .build();

            milvusClient.insert(insertParam);
            logger.debug("向量插入成功，文本长度: {}", texts.get(0).length());
            
            return UUID.randomUUID().toString();
        } catch (Exception e) {
            logger.error("插入向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("插入向量失败", e);
        }
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        return embeddings.stream()
                .map(this::add)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> embedded) {
        if (embeddings.size() != embedded.size()) {
            throw new IllegalArgumentException("嵌入向量和文本段数量不匹配");
        }

        List<String> ids = new ArrayList<>();
        for (int i = 0; i < embeddings.size(); i++) {
            String id = add(embeddings.get(i), embedded.get(i));
            ids.add(id);
        }
        return ids;
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest request) {
        try {
            List<List<Float>> searchVectors = Collections.singletonList(request.queryEmbedding().vectorAsList());

            SearchParam searchParam = SearchParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withMetricType(io.milvus.grpc.MetricType.COSINE)
                    .withOutFields(Arrays.asList("text", "knowledge_base_id", "document_id", "document_name"))
                    .withTopK(request.maxResults())
                    .withVectors(searchVectors)
                    .withVectorFieldName("vector")
                    .withParams("{\"nprobe\":10}")
                    .build();

            SearchResults searchResults = milvusClient.search(searchParam).getData().getResults();
            SearchResultsWrapper wrapper = new SearchResultsWrapper(searchResults);

            List<EmbeddingMatch<TextSegment>> matches = new ArrayList<>();
            
            for (int i = 0; i < wrapper.getRowCount(); i++) {
                float score = wrapper.getIDScore(0).get(i).getScore();
                
                // 应用最小分数过滤
                if (score < request.minScore()) {
                    continue;
                }

                String text = (String) wrapper.getFieldData("text", 0).get(i);
                Long knowledgeBaseId = (Long) wrapper.getFieldData("knowledge_base_id", 0).get(i);
                Long documentId = (Long) wrapper.getFieldData("document_id", 0).get(i);
                String documentName = (String) wrapper.getFieldData("document_name", 0).get(i);

                // 创建TextSegment
                TextSegment textSegment = TextSegment.from(text);
                if (knowledgeBaseId != null) {
                    textSegment.metadata().put("knowledgeBaseId", knowledgeBaseId.toString());
                }
                if (documentId != null) {
                    textSegment.metadata().put("documentId", documentId.toString());
                }
                if (documentName != null) {
                    textSegment.metadata().put("documentName", documentName);
                }

                EmbeddingMatch<TextSegment> match = new EmbeddingMatch<>(
                        score,
                        wrapper.getIDScore(0).get(i).getLongID().toString(),
                        null, // embedding不返回
                        textSegment
                );

                matches.add(match);
            }

            logger.debug("搜索完成，找到 {} 个匹配项", matches.size());
            return new EmbeddingSearchResult<>(matches);

        } catch (Exception e) {
            logger.error("搜索向量失败: {}", e.getMessage(), e);
            throw new RuntimeException("搜索向量失败", e);
        }
    }
}
