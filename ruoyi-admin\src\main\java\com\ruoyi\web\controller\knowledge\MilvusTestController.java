package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.collection.GetCollectionStatisticsParam;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.response.GetCollStatResponseWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Milvus测试控制器
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@RestController
@RequestMapping("/knowledge/milvus/test")
public class MilvusTestController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MilvusTestController.class);

    @Autowired(required = false)
    private MilvusServiceClient milvusServiceClient;

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    @Autowired
    private EmbeddingModel embeddingModel;

    @Value("${milvus.collection.name:knowledge_base_vectors}")
    private String collectionName;

    /**
     * 测试Milvus连接
     */
    @GetMapping("/connection")
    public AjaxResult testConnection() {
        try {
            if (milvusServiceClient == null) {
                return AjaxResult.error("Milvus客户端未初始化，请检查配置");
            }

            // 检查集合是否存在
            HasCollectionParam hasCollectionParam = HasCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            boolean hasCollection = milvusServiceClient.hasCollection(hasCollectionParam).getData();

            Map<String, Object> result = new HashMap<>();
            result.put("connected", true);
            result.put("collectionExists", hasCollection);
            result.put("collectionName", collectionName);

            if (hasCollection) {
                // 获取集合统计信息
                GetCollectionStatisticsParam statsParam = GetCollectionStatisticsParam.newBuilder()
                        .withCollectionName(collectionName)
                        .build();

                GetCollStatResponseWrapper stats = new GetCollStatResponseWrapper(
                        milvusServiceClient.getCollectionStatistics(statsParam).getData());
                result.put("rowCount", stats.getRowCount());
            }

            return AjaxResult.success("Milvus连接正常", result);

        } catch (Exception e) {
            logger.error("测试Milvus连接失败: {}", e.getMessage(), e);
            return AjaxResult.error("Milvus连接失败: " + e.getMessage());
        }
    }

    /**
     * 测试向量存储
     */
    @PostMapping("/store")
    public AjaxResult testStore(@RequestParam String text) {
        try {
            // 创建测试文本段
            TextSegment textSegment = TextSegment.from(text);
            textSegment.metadata().put("knowledgeBaseId", "999");
            textSegment.metadata().put("documentId", "888");
            textSegment.metadata().put("documentName", "测试文档");

            // 计算嵌入向量
            Embedding embedding = embeddingModel.embed(textSegment).content();

            // 存储到向量数据库
            String id = embeddingStore.add(embedding, textSegment);

            Map<String, Object> result = new HashMap<>();
            result.put("id", id);
            result.put("text", text);
            result.put("vectorDimension", embedding.dimension());

            return AjaxResult.success("向量存储成功", result);

        } catch (Exception e) {
            logger.error("测试向量存储失败: {}", e.getMessage(), e);
            return AjaxResult.error("向量存储失败: " + e.getMessage());
        }
    }

    /**
     * 测试向量搜索
     */
    @PostMapping("/search")
    public AjaxResult testSearch(@RequestParam String query, 
                                @RequestParam(defaultValue = "5") int maxResults,
                                @RequestParam(defaultValue = "0.6") double minScore) {
        try {
            // 计算查询向量
            Embedding queryEmbedding = embeddingModel.embed(query).content();

            // 构建搜索请求
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(maxResults)
                    .minScore(minScore)
                    .build();

            // 执行搜索
            EmbeddingSearchResult<TextSegment> searchResult = embeddingStore.search(searchRequest);
            List<EmbeddingMatch<TextSegment>> matches = searchResult.matches();

            Map<String, Object> result = new HashMap<>();
            result.put("query", query);
            result.put("matchCount", matches.size());
            result.put("matches", matches.stream().map(match -> {
                Map<String, Object> matchInfo = new HashMap<>();
                matchInfo.put("score", match.score());
                matchInfo.put("text", match.embedded().text());
                matchInfo.put("metadata", match.embedded().metadata().toMap());
                return matchInfo;
            }).toArray());

            return AjaxResult.success("向量搜索成功", result);

        } catch (Exception e) {
            logger.error("测试向量搜索失败: {}", e.getMessage(), e);
            return AjaxResult.error("向量搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取集合统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            if (milvusServiceClient == null) {
                return AjaxResult.error("Milvus客户端未初始化");
            }

            GetCollectionStatisticsParam statsParam = GetCollectionStatisticsParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            GetCollStatResponseWrapper stats = new GetCollStatResponseWrapper(
                    milvusServiceClient.getCollectionStatistics(statsParam).getData());

            Map<String, Object> result = new HashMap<>();
            result.put("collectionName", collectionName);
            result.put("rowCount", stats.getRowCount());

            return AjaxResult.success("获取统计信息成功", result);

        } catch (Exception e) {
            logger.error("获取集合统计信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取统计信息失败: " + e.getMessage());
        }
    }
}
