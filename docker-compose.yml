version: '3.8'

services:
  # Milvus向量数据库
  milvus:
    image: milvusdb/milvus:v2.3.4
    container_name: milvus-standalone
    ports:
      - "19530:19530"  # Milvus gRPC端口
      - "9091:9091"    # Milvus HTTP端口
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - milvus_data:/var/lib/milvus
      - ./milvus.yaml:/milvus/configs/milvus.yaml
    depends_on:
      - etcd
      - minio
    restart: unless-stopped
    networks:
      - milvus-network

  # etcd - Milvus元数据存储
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    container_name: milvus-etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    restart: unless-stopped
    networks:
      - milvus-network

  # MinIO - Milvus对象存储
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: milvus-minio
    ports:
      - "9000:9000"    # MinIO API端口
      - "9001:9001"    # MinIO Console端口
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    volumes:
      - minio_data:/data
    command: minio server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    restart: unless-stopped
    networks:
      - milvus-network

  # Attu - Milvus管理界面（可选）
  attu:
    image: zilliz/attu:v2.3.4
    container_name: milvus-attu
    ports:
      - "3000:3000"
    environment:
      - MILVUS_URL=milvus:19530
    depends_on:
      - milvus
    restart: unless-stopped
    networks:
      - milvus-network

volumes:
  milvus_data:
  etcd_data:
  minio_data:

networks:
  milvus-network:
    driver: bridge
