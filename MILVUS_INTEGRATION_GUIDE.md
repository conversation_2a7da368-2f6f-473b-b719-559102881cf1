# Milvus向量数据库集成指南

## 📋 概述

本项目已成功集成Milvus向量数据库，实现知识库的持久化存储。Milvus是一个开源的向量数据库，专门为AI应用设计，支持大规模向量数据的存储和检索。

## 🚀 部署步骤

### 1. 启动Milvus服务

使用Docker Compose启动Milvus及其依赖服务：

```bash
# 启动Milvus服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs milvus
```

### 2. 验证Milvus服务

访问Attu管理界面（可选）：
- URL: http://localhost:3000
- 可以通过Web界面管理Milvus集合和数据

### 3. 配置应用

在 `application.yml` 中配置Milvus：

```yaml
# 知识库配置
knowledge:
  embedding:
    store:
      # 使用milvus存储
      type: milvus

# Milvus向量数据库配置
milvus:
  host: localhost
  port: 19530
  collection:
    name: knowledge_base_vectors
  vector:
    dimension: 384
```

### 4. 启动应用

```bash
# 编译项目
mvn clean package -Dmaven.test.skip=true

# 启动应用
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

## 🔧 功能特性

### 1. 持久化存储
- 向量数据存储在Milvus数据库中，重启后数据不丢失
- 支持大规模向量数据存储和检索
- 高性能的相似度搜索

### 2. 自动集合管理
- 应用启动时自动创建Milvus集合
- 自动创建向量索引（IVF_FLAT + COSINE）
- 支持元数据存储（知识库ID、文档ID、文档名称等）

### 3. 兼容性
- 完全兼容现有的知识库API
- 支持多种存储方式切换（内存、Redis、Milvus）
- 无需修改现有业务代码

## 🧪 测试功能

### 1. 测试API端点

```bash
# 测试Milvus连接
GET /knowledge/milvus/test/connection

# 测试向量存储
POST /knowledge/milvus/test/store
Content-Type: application/x-www-form-urlencoded
text=这是一个测试文档的内容

# 测试向量搜索
POST /knowledge/milvus/test/search
Content-Type: application/x-www-form-urlencoded
query=测试文档&maxResults=5&minScore=0.6

# 获取集合统计信息
GET /knowledge/milvus/test/stats
```

### 2. 测试步骤

1. **连接测试**：访问连接测试API，确认Milvus服务正常
2. **存储测试**：使用存储API添加测试向量
3. **搜索测试**：使用搜索API验证向量检索功能
4. **统计查看**：查看集合中的数据统计

## 📊 架构说明

### 1. 组件架构

```
应用层
├── KnowledgeRagService (业务逻辑)
├── KnowledgeRagConfig (配置管理)
└── MilvusTestController (测试接口)

存储层
├── MilvusEmbeddingStore (Milvus存储实现)
├── MilvusConfig (Milvus配置)
└── MilvusServiceClient (Milvus客户端)

基础设施层
├── Milvus (向量数据库)
├── etcd (元数据存储)
└── MinIO (对象存储)
```

### 2. 数据模型

Milvus集合字段：
- `id`: 主键（自动生成）
- `vector`: 向量数据（384维）
- `text`: 文本内容
- `knowledge_base_id`: 知识库ID
- `document_id`: 文档ID
- `document_name`: 文档名称

## 🔧 配置说明

### 1. 存储类型切换

在 `application.yml` 中修改存储类型：

```yaml
knowledge:
  embedding:
    store:
      type: memory   # 内存存储（默认）
      # type: redis  # Redis存储
      # type: milvus # Milvus存储
```

### 2. Milvus配置参数

```yaml
milvus:
  host: localhost          # Milvus服务地址
  port: 19530             # Milvus服务端口
  collection:
    name: knowledge_base_vectors  # 集合名称
  vector:
    dimension: 384        # 向量维度
```

## 🚨 故障排除

### 1. 常见问题

**Milvus连接失败**
- 检查Docker服务是否启动：`docker-compose ps`
- 检查端口是否被占用：`netstat -an | grep 19530`
- 查看Milvus日志：`docker-compose logs milvus`

**集合创建失败**
- 确认Milvus服务完全启动
- 检查etcd和MinIO服务状态
- 查看应用日志中的错误信息

**向量搜索无结果**
- 确认集合中有数据：访问统计API
- 检查搜索参数（minScore可能过高）
- 验证向量维度是否匹配

### 2. 性能优化

**索引优化**
- 根据数据量调整nlist参数
- 考虑使用其他索引类型（IVF_SQ8、HNSW等）

**搜索优化**
- 调整nprobe参数平衡精度和性能
- 使用合适的相似度阈值

## 📈 监控和维护

### 1. 数据监控

- 使用Attu界面监控集合状态
- 定期检查数据量和索引状态
- 监控搜索性能指标

### 2. 数据备份

```bash
# 备份Milvus数据
docker-compose exec milvus-minio mc cp -r /data/a-bucket /backup/

# 备份etcd数据
docker-compose exec milvus-etcd etcdctl snapshot save /backup/etcd-snapshot.db
```

## 🔄 迁移说明

### 从内存存储迁移到Milvus

1. 备份现有知识库数据（如果需要）
2. 修改配置文件存储类型为milvus
3. 重启应用，系统会自动创建Milvus集合
4. 重新构建知识库（现有内存数据会丢失）

### 数据迁移工具

如需保留现有数据，可以开发数据迁移工具：
1. 从现有存储读取向量数据
2. 批量插入到Milvus集合
3. 验证数据完整性

## 📚 相关文档

- [Milvus官方文档](https://milvus.io/docs)
- [Milvus Java SDK](https://github.com/milvus-io/milvus-sdk-java)
- [LangChain4j文档](https://docs.langchain4j.dev/)
