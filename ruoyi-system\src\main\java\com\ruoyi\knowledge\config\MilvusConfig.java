package com.ruoyi.knowledge.config;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.collection.CreateCollectionParam;
import io.milvus.param.collection.FieldType;
import io.milvus.param.collection.HasCollectionParam;
import io.milvus.param.index.CreateIndexParam;
import io.milvus.common.clientenum.ConsistencyLevelEnum;
import io.milvus.grpc.DataType;
import io.milvus.grpc.MetricType;
import io.milvus.param.IndexType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Arrays;

/**
 * Milvus向量数据库配置类
 * 
 * <AUTHOR>
 * @date 2024-12-12
 */
@Configuration
public class MilvusConfig {

    private static final Logger logger = LoggerFactory.getLogger(MilvusConfig.class);

    @Value("${milvus.host:localhost}")
    private String milvusHost;

    @Value("${milvus.port:19530}")
    private int milvusPort;

    @Value("${milvus.collection.name:knowledge_base_vectors}")
    private String collectionName;

    @Value("${milvus.vector.dimension:384}")
    private int vectorDimension;

    private MilvusServiceClient milvusClient;

    /**
     * 创建Milvus客户端Bean
     */
    @Bean
    public MilvusServiceClient milvusServiceClient() {
        ConnectParam connectParam = ConnectParam.newBuilder()
                .withHost(milvusHost)
                .withPort(milvusPort)
                .build();

        milvusClient = new MilvusServiceClient(connectParam);
        logger.info("Milvus客户端连接成功: {}:{}", milvusHost, milvusPort);

        // 初始化集合
        initializeCollection();

        return milvusClient;
    }

    /**
     * 初始化Milvus集合
     */
    private void initializeCollection() {
        try {
            // 检查集合是否存在
            HasCollectionParam hasCollectionParam = HasCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            boolean hasCollection = milvusClient.hasCollection(hasCollectionParam).getData();

            if (!hasCollection) {
                logger.info("创建Milvus集合: {}", collectionName);
                createCollection();
                createIndex();
                logger.info("Milvus集合创建完成: {}", collectionName);
            } else {
                logger.info("Milvus集合已存在: {}", collectionName);
            }
        } catch (Exception e) {
            logger.error("初始化Milvus集合失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化Milvus集合失败", e);
        }
    }

    /**
     * 创建集合
     */
    private void createCollection() {
        // 定义字段
        FieldType idField = FieldType.newBuilder()
                .withName("id")
                .withDataType(DataType.Int64)
                .withPrimaryKey(true)
                .withAutoID(true)
                .build();

        FieldType vectorField = FieldType.newBuilder()
                .withName("vector")
                .withDataType(DataType.FloatVector)
                .withDimension(vectorDimension)
                .build();

        FieldType textField = FieldType.newBuilder()
                .withName("text")
                .withDataType(DataType.VarChar)
                .withMaxLength(65535)
                .build();

        FieldType knowledgeBaseIdField = FieldType.newBuilder()
                .withName("knowledge_base_id")
                .withDataType(DataType.Int64)
                .build();

        FieldType documentIdField = FieldType.newBuilder()
                .withName("document_id")
                .withDataType(DataType.Int64)
                .build();

        FieldType documentNameField = FieldType.newBuilder()
                .withName("document_name")
                .withDataType(DataType.VarChar)
                .withMaxLength(255)
                .build();

        // 创建集合
        CreateCollectionParam createCollectionParam = CreateCollectionParam.newBuilder()
                .withCollectionName(collectionName)
                .withDescription("知识库向量存储集合")
                .withShardsNum(2)
                .withFieldTypes(Arrays.asList(
                        idField,
                        vectorField,
                        textField,
                        knowledgeBaseIdField,
                        documentIdField,
                        documentNameField
                ))
                .withConsistencyLevel(ConsistencyLevelEnum.STRONG)
                .build();

        milvusClient.createCollection(createCollectionParam);
    }

    /**
     * 创建向量索引
     */
    private void createIndex() {
        CreateIndexParam createIndexParam = CreateIndexParam.newBuilder()
                .withCollectionName(collectionName)
                .withFieldName("vector")
                .withIndexType(IndexType.IVF_FLAT)
                .withMetricType(MetricType.COSINE)
                .withExtraParam("{\"nlist\":1024}")
                .build();

        milvusClient.createIndex(createIndexParam);
    }

    /**
     * 获取集合名称
     */
    public String getCollectionName() {
        return collectionName;
    }

    /**
     * 获取向量维度
     */
    public int getVectorDimension() {
        return vectorDimension;
    }

    /**
     * 销毁时关闭连接
     */
    @PreDestroy
    public void destroy() {
        if (milvusClient != null) {
            try {
                milvusClient.close();
                logger.info("Milvus客户端连接已关闭");
            } catch (Exception e) {
                logger.error("关闭Milvus客户端连接失败: {}", e.getMessage(), e);
            }
        }
    }
}
